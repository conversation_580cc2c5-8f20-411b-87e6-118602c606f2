# FastAPI WebSocket聊天服务

这是一个基于FastAPI的WebSocket聊天服务，专注于提供实时消息通信功能。该服务已经从原始的全功能聊天应用中分离出来，现在只负责WebSocket连接管理、实时消息处理和分发、心跳检测机制以及Redis发布订阅。

## 系统架构

```
+----------------+      +----------------+
|                |      |                |
|  PHP 应用      |<---->|  FastAPI 应用  |
|  (页面和API)   |      |  (WebSocket)   |
|                |      |                |
+-------+--------+      +--------+-------+
        |                        |
        v                        v
+-------+------------------------+-------+
|                                        |
|             共享数据库                  |
|          (MySQL + Redis)               |
|                                        |
+----------------------------------------+
```

### 职责划分

**FastAPI 负责**:
- WebSocket连接管理
- 实时消息处理和分发
- 心跳检测机制
- Redis发布订阅

**PHP 负责**:
- 用户认证和会话管理
- 聊天列表和聊天室管理
- 消息历史记录查询
- 文件上传处理
- 卡片消息管理
- 前端页面渲染

## 技术栈

### 后端
- **FastAPI**: 高性能异步Web框架
- **WebSocket**: 实时通信协议
- **Redis**: 消息队列和发布订阅
- **MySQL**: 数据持久化
- **SQLAlchemy**: ORM数据库操作
- **Uvicorn**: ASGI服务器
- **PyJWT**: JWT令牌验证

## 安装与运行

### 前提条件
- Python 3.7+
- Redis服务器
- MySQL数据库

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置
编辑`config/dev_config.toml`文件，设置数据库和Redis连接信息以及JWT密钥：

```toml
[db_redis]
host = "127.0.0.1"
port = 6379
db = 10
password = "123456"
timeout = 60000

[db_mysql]
host = "127.0.0.1"
port = 3306
user = "root"
psd = "root"
database = "test"

[jwt]
secret_key = "your_secret_key_here"
algorithm = "HS256"
access_token_expire_minutes = 1440  # 24小时
```

### 运行FastAPI服务
```bash
uvicorn main:app --host 0.0.0.0 --port 298
```

### 注意事项
- 确保JWT密钥在PHP和FastAPI之间保持一致
- 生产环境中使用HTTPS和WSS协议
- 设置合理的令牌过期时间
- 在生产环境中限制CORS来源

## API文档

应用运行后，访问http://localhost:298/docs可以查看API文档，包含以下主要API：

- `/api/chat/connect_chat_with_token`: 基于令牌的WebSocket连接
- `/api/chat/send_message`: 发送消息
- `/api/chat/edit_message`: 编辑消息
- `/api/chat/create_group`: 创建群组

## WebSocket通信

### 连接WebSocket

```
ws://localhost:298/api/chat/connect_chat_with_token?token={JWT令牌}
```

### 消息类型

#### 基本消息类型

| 消息类型 | 描述 | 字段 |
|---------|------|------|
| private_message | 私聊消息 | type, msg, sender, recipient |
| group_message | 群聊消息 | type, msg, sender, chatroom_id |
| heartbeat | 心跳消息 | type, timestamp |
| system | 系统消息 | type, msg, sender |

#### 特殊消息类型

| 消息类型 | 描述 | 字段 |
|---------|------|------|
| card_confirmation | 卡片确认消息 | type, card_id, card_type, user_id, chatroom_id, confirmed_at |
| edit_message | 编辑消息 | type, message_id, content, sender |
| create_group | 创建群组 | type, name, members, sender |

### 心跳机制

为保持WebSocket连接活跃，客户端需要定期发送心跳消息：

```javascript
// 前端JavaScript代码
function startHeartbeat(socket) {
    const heartbeatInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
                type: "heartbeat",
                timestamp: Date.now()
            }));
        } else {
            clearInterval(heartbeatInterval);
        }
    }, 30000); // 每30秒发送一次

    return heartbeatInterval;
}
```

## 目录结构

```
fastapi_ws_chat/
├── app/
│   ├── chat_manager/
│   │   ├── chat.py         # WebSocket路由
│   │   └── server.py       # WebSocket连接管理
│   ├── models.py           # 数据库模型
│   ├── database.py         # 数据库连接
│   └── utils/
│       ├── message_filter.py  # 消息过滤
│       └── token_util.py      # 令牌验证
├── config/
│   ├── dev_config.toml     # 开发环境配置
│   └── get_config.py       # 配置加载
├── utils/
│   ├── redis_util.py       # Redis工具
│   └── redis_queue.py      # Redis消息队列
├── main.py                 # 主应用入口
├── requirements.txt        # 依赖列表
├── PHP与FastAPI通信指南.md   # 通信指南
└── php_examples/           # PHP示例代码
```

## 系统通信流程

1. PHP应用生成JWT令牌并提供给前端
2. 前端通过WebSocket与FastAPI建立连接，使用JWT令牌认证
3. FastAPI验证令牌并维护WebSocket连接
4. 消息通过两种方式发送：
   - 前端直接通过WebSocket发送
   - PHP后端通过HTTP API或Redis发布
5. FastAPI接收消息并通过Redis发布订阅机制分发
6. FastAPI将消息推送给在线用户或存储为离线消息
7. 用户重新连接时自动同步离线消息

## 心跳机制

- 客户端每30秒发送一次心跳包
- 服务器响应心跳并记录最后活动时间
- 服务器每30秒检查一次心跳超时（120秒）
- 超时连接自动断开并清理资源

## PHP集成

详细的PHP集成指南请参考[PHP与FastAPI通信指南](PHP与FastAPI通信指南.md)。

`php_examples`目录包含了PHP应用与FastAPI WebSocket服务集成的示例代码：

- `index.php` - 登录页面
- `chat.php` - 聊天页面
- `api/chatrooms.php` - 聊天室API
- `api/messages.php` - 消息API
- `includes/token.php` - JWT令牌工具
- `includes/redis_publisher.php` - Redis消息发布工具

